<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Demo Role Categories -->
        <record id="demo_category_temp" model="th.role.category">
            <field name="name">Demo Temporary Roles</field>
            <field name="description">Temporary demo roles for testing</field>
        </record>

        <!-- Demo Roles -->
        <record id="demo_role_temp_admin" model="th.user.role">
            <field name="name">Temporary Administrator</field>
            <field name="code">temp_admin</field>
            <field name="description">Temporary administrator access for special projects</field>
            <field name="th_category_id" ref="demo_category_temp"/>
            <field name="group_ids" eval="[(6, 0, [ref('base.group_system')])]"/>
        </record>

        <record id="demo_role_project_manager" model="th.user.role">
            <field name="name">Project Manager</field>
            <field name="code">project_mgr</field>
            <field name="description">Project manager with limited administrative access</field>
            <field name="th_category_id" ref="th_role_category_project"/>
            <field name="group_ids" eval="[(6, 0, [ref('base.group_user')])]"/>
        </record>

        <record id="demo_role_sales_temp" model="th.user.role">
            <field name="name">Temporary Sales Access</field>
            <field name="code">sales_temp</field>
            <field name="description">Temporary sales access for consultants</field>
            <field name="th_category_id" ref="th_role_category_sales"/>
            <field name="group_ids" eval="[(6, 0, [ref('base.group_user')])]"/>
        </record>

        <!-- Demo Role Assignments -->
        <record id="demo_assignment_1" model="th.user.role.line">
            <field name="th_role_id" ref="demo_role_temp_admin"/>
            <field name="th_user_id" ref="base.user_demo"/>
            <field name="th_date_from" eval="(DateTime.now() - timedelta(days=30))"/>
            <field name="th_date_to" eval="(DateTime.now() + timedelta(days=30))"/>
            <field name="th_notes">Demo assignment for testing purposes</field>
            <field name="th_active">True</field>
        </record>

        <record id="demo_assignment_2" model="th.user.role.line">
            <field name="th_role_id" ref="demo_role_project_manager"/>
            <field name="th_user_id" ref="base.user_demo"/>
            <field name="th_date_from" eval="(DateTime.now() - timedelta(days=10))"/>
            <field name="th_date_to" eval="(DateTime.now() + timedelta(days=60))"/>
            <field name="th_notes">Project manager role for Q1 project</field>
            <field name="th_active">True</field>
        </record>

        <record id="demo_assignment_3" model="th.user.role.line">
            <field name="th_role_id" ref="demo_role_sales_temp"/>
            <field name="th_user_id" ref="base.user_admin"/>
            <field name="th_date_from" eval="(DateTime.now() + timedelta(days=7))"/>
            <field name="th_date_to" eval="(DateTime.now() + timedelta(days=37))"/>
            <field name="th_notes">Temporary sales access for admin user</field>
            <field name="th_active">True</field>
        </record>

        <!-- Demo History Records -->
        <record id="demo_history_1" model="th.role.history">
            <field name="th_user_id" ref="base.user_demo"/>
            <field name="th_role_id" ref="demo_role_temp_admin"/>
            <field name="th_date" eval="(DateTime.now() - timedelta(days=30))"/>
            <field name="th_action">activate</field>
            <field name="th_performed_by" ref="base.user_admin"/>
            <field name="th_reason">Demo role activated automatically</field>
            <field name="th_role_line_id" ref="demo_assignment_1"/>
        </record>

        <record id="demo_history_2" model="th.role.history">
            <field name="th_user_id" ref="base.user_demo"/>
            <field name="th_role_id" ref="demo_role_project_manager"/>
            <field name="th_date" eval="(DateTime.now() - timedelta(days=10))"/>
            <field name="th_action">activate</field>
            <field name="th_performed_by" ref="base.user_admin"/>
            <field name="th_reason">Project manager role activated</field>
            <field name="th_role_line_id" ref="demo_assignment_2"/>
        </record>

    </data>
</odoo>
