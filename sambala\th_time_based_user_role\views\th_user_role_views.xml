<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Tree View -->
        <record id="view_th_user_role_tree" model="ir.ui.view">
            <field name="name">th.user.role.tree</field>
            <field name="model">th.user.role</field>
            <field name="arch" type="xml">
                <tree string="Time-Based User Roles" decoration-muted="not active">
                    <field name="th_sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="code"/>
                    <field name="th_category_id"/>
                    <field name="th_user_count" string="Users"/>
                    <field name="th_active_user_count" string="Active"/>
                    <field name="active"/>
                </tree>
            </field>
        </record>

        <!-- Form View -->
        <record id="view_th_user_role_form" model="ir.ui.view">
            <field name="name">th.user.role.form</field>
            <field name="model">th.user.role</field>
            <field name="arch" type="xml">
                <form string="Time-Based User Role">
                    <header>
                        <button name="action_create_role_assignment" 
                                string="Create Assignment" 
                                type="object" 
                                class="btn-primary"/>
                        <button name="action_view_users" 
                                string="View Users" 
                                type="object" 
                                class="btn-secondary"/>
                        <button name="action_view_role_lines" 
                                string="View Assignments" 
                                type="object" 
                                class="btn-secondary"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_users" 
                                    type="object" 
                                    class="oe_stat_button" 
                                    icon="fa-users">
                                <field name="th_user_count" widget="statinfo" string="Users"/>
                            </button>
                            <button name="action_view_role_lines" 
                                    type="object" 
                                    class="oe_stat_button" 
                                    icon="fa-calendar">
                                <field name="th_active_user_count" widget="statinfo" string="Active"/>
                            </button>
                        </div>
                        
                        <widget name="web_ribbon" title="Archived" bg_color="bg-danger" 
                                attrs="{'invisible': [('active', '=', True)]}"/>
                        
                        <div class="oe_title">
                            <label for="name" class="oe_edit_only"/>
                            <h1>
                                <field name="name" placeholder="Role Name..."/>
                            </h1>
                            <label for="code" class="oe_edit_only"/>
                            <h2>
                                <field name="code" placeholder="Role Code..."/>
                            </h2>
                        </div>
                        
                        <group>
                            <group>
                                <field name="th_category_id"/>
                                <field name="th_sequence"/>
                                <field name="active"/>
                            </group>
                            <group>
                                <field name="th_user_count" readonly="1"/>
                                <field name="th_active_user_count" readonly="1"/>
                            </group>
                        </group>
                        
                        <field name="description" placeholder="Description of the role..."/>
                        
                        <notebook>
                            <page string="Access Groups" name="groups">
                                <field name="group_ids">
                                    <tree>
                                        <field name="category_id"/>
                                        <field name="name"/>
                                        <field name="comment"/>
                                    </tree>
                                </field>
                            </page>
                            
                            <page string="Users" name="users">
                                <field name="th_user_ids" readonly="1">
                                    <tree>
                                        <field name="name"/>
                                        <field name="login"/>
                                        <field name="email"/>
                                        <field name="active"/>
                                    </tree>
                                </field>
                            </page>
                            
                            <page string="Assignments" name="assignments">
                                <field name="th_role_line_ids">
                                    <tree decoration-success="th_is_enabled == True" 
                                          decoration-warning="th_status == 'pending'" 
                                          decoration-danger="th_status == 'expired'">
                                        <field name="th_user_id"/>
                                        <field name="th_date_from"/>
                                        <field name="th_date_to"/>
                                        <field name="th_status"/>
                                        <field name="th_is_enabled" widget="boolean_toggle"/>
                                        <field name="th_active"/>
                                        <button name="action_enable_role" 
                                                string="Enable" 
                                                type="object" 
                                                icon="fa-play" 
                                                attrs="{'invisible': [('th_is_enabled', '=', True)]}"/>
                                        <button name="action_disable_role" 
                                                string="Disable" 
                                                type="object" 
                                                icon="fa-pause" 
                                                attrs="{'invisible': [('th_is_enabled', '=', False)]}"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="activity_ids" widget="mail_activity"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Kanban View -->
        <record id="view_th_user_role_kanban" model="ir.ui.view">
            <field name="name">th.user.role.kanban</field>
            <field name="model">th.user.role</field>
            <field name="arch" type="xml">
                <kanban default_group_by="th_category_id" class="o_kanban_small_column">
                    <field name="name"/>
                    <field name="code"/>
                    <field name="th_category_id"/>
                    <field name="th_user_count"/>
                    <field name="th_active_user_count"/>
                    <field name="active"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_card oe_kanban_global_click">
                                <div class="oe_kanban_content">
                                    <div class="o_kanban_record_top">
                                        <div class="o_kanban_record_headings">
                                            <strong class="o_kanban_record_title">
                                                <field name="name"/>
                                            </strong>
                                            <br/>
                                            <small class="o_kanban_record_subtitle text-muted">
                                                [<field name="code"/>]
                                            </small>
                                        </div>
                                    </div>
                                    <div class="o_kanban_record_body">
                                        <div t-if="record.description.raw_value">
                                            <field name="description"/>
                                        </div>
                                    </div>
                                    <div class="o_kanban_record_bottom">
                                        <div class="oe_kanban_bottom_left">
                                            <span class="badge badge-pill badge-info">
                                                <i class="fa fa-users"/> <field name="th_user_count"/>
                                            </span>
                                            <span class="badge badge-pill badge-success">
                                                <i class="fa fa-check"/> <field name="th_active_user_count"/>
                                            </span>
                                        </div>
                                        <div class="oe_kanban_bottom_right">
                                            <t t-if="!record.active.raw_value">
                                                <span class="badge badge-pill badge-secondary">Archived</span>
                                            </t>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <!-- Search View -->
        <record id="view_th_user_role_search" model="ir.ui.view">
            <field name="name">th.user.role.search</field>
            <field name="model">th.user.role</field>
            <field name="arch" type="xml">
                <search string="Search Roles">
                    <field name="name" string="Role Name"/>
                    <field name="code" string="Role Code"/>
                    <field name="description"/>
                    <field name="th_category_id"/>
                    <field name="group_ids"/>
                    <field name="th_active_user_count"/>
                    
                    <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                    <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                    
                    <separator/>
                    <filter string="Has Users" name="has_users" domain="[('th_user_count', '>', 0)]"/>
                    <filter string="No Users" name="no_users" domain="[('th_user_count', '=', 0)]"/>
                    
                    <separator/>
                    <group expand="0" string="Group By">
                        <filter string="Category" name="group_category" context="{'group_by': 'th_category_id'}"/>
                        <filter string="Active" name="group_active" context="{'group_by': 'active'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Action -->
        <record id="action_th_user_role" model="ir.actions.act_window">
            <field name="name">Time-Based Roles</field>
            <field name="res_model">th.user.role</field>
            <field name="view_mode">kanban,tree,form</field>
            <field name="context">{'search_default_active': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first time-based role!
                </p>
                <p>
                    Time-based roles allow you to automatically grant and revoke 
                    user permissions based on time constraints.
                </p>
            </field>
        </record>

    </data>
</odoo>
