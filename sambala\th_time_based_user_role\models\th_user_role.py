# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError


class ThUserRole(models.Model):
    _name = 'th.user.role'
    _description = 'Time-Based User Role'
    _order = 'th_sequence, name'
    _rec_name = 'name'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(
        string='Role Name',
        required=True,
        tracking=True,
        help="Name of the role"
    )
    
    code = fields.Char(
        string='Role Code',
        required=True,
        tracking=True,
        help="Unique code for the role"
    )
    
    description = fields.Text(
        string='Description',
        tracking=True,
        help="Detailed description of the role"
    )
    
    active = fields.Boolean(
        string='Active',
        default=True,
        tracking=True,
        help="If unchecked, it will allow you to hide the role without removing it."
    )
    
    group_ids = fields.Many2many(
        'res.groups',
        'th_user_role_group_rel',
        'role_id',
        'group_id',
        string='Access Groups',
        tracking=True,
        help="Groups that will be assigned to users with this role"
    )
    
    th_role_line_ids = fields.One2many(
        'th.user.role.line',
        'th_role_id',
        string='Role Assignments',
        help="Users assigned to this role with time constraints"
    )
    
    th_user_ids = fields.Many2many(
        'res.users',
        string='Users',
        compute='_compute_th_user_ids',
        store=True,
        help="Users who have this role assigned"
    )
    
    th_category_id = fields.Many2one(
        'th.role.category',
        string='Category',
        tracking=True,
        help="Category of this role"
    )
    
    th_sequence = fields.Integer(
        string='Sequence',
        default=10,
        help="Sequence for ordering roles"
    )
    
    th_user_count = fields.Integer(
        string='Number of Users',
        compute='_compute_th_user_count',
        store=True,
        help="Number of users assigned to this role"
    )
    
    th_active_user_count = fields.Integer(
        string='Active Users',
        compute='_compute_th_active_user_count',
        help="Number of users with active role assignments"
    )

    _sql_constraints = [
        ('unique_code', 'unique(code)', 'Role code must be unique!'),
    ]

    @api.depends('th_role_line_ids.th_user_id')
    def _compute_th_user_ids(self):
        for role in self:
            role.th_user_ids = role.th_role_line_ids.mapped('th_user_id')

    @api.depends('th_user_ids')
    def _compute_th_user_count(self):
        for role in self:
            role.th_user_count = len(role.th_user_ids)

    @api.depends('th_role_line_ids.th_is_enabled')
    def _compute_th_active_user_count(self):
        for role in self:
            role.th_active_user_count = len(
                role.th_role_line_ids.filtered('th_is_enabled')
            )

    @api.constrains('code')
    def _check_code(self):
        for role in self:
            if not role.code:
                raise ValidationError(_('Role code is required'))
            if not role.code.replace('_', '').replace('-', '').isalnum():
                raise ValidationError(
                    _('Role code can only contain letters, numbers, underscores and hyphens')
                )

    def name_get(self):
        result = []
        for role in self:
            name = f"[{role.code}] {role.name}"
            result.append((role.id, name))
        return result

    @api.model
    def name_search(self, name='', args=None, operator='ilike', limit=100):
        args = args or []
        domain = []
        if name:
            domain = [
                '|', '|',
                ('name', operator, name),
                ('code', operator, name),
                ('description', operator, name)
            ]
        roles = self.search(domain + args, limit=limit)
        return roles.name_get()

    def action_view_users(self):
        """Action to view users assigned to this role"""
        self.ensure_one()
        action = self.env.ref('base.action_res_users').read()[0]
        action['domain'] = [('id', 'in', self.th_user_ids.ids)]
        action['context'] = {
            'default_th_role_ids': [(6, 0, [self.id])]
        }
        return action

    def action_view_role_lines(self):
        """Action to view role assignments"""
        self.ensure_one()
        action = self.env.ref('th_time_based_user_role.action_th_user_role_line').read()[0]
        action['domain'] = [('th_role_id', '=', self.id)]
        action['context'] = {
            'default_th_role_id': self.id
        }
        return action

    def action_create_role_assignment(self):
        """Action to create new role assignment"""
        self.ensure_one()
        return {
            'name': _('Create Role Assignment'),
            'type': 'ir.actions.act_window',
            'res_model': 'th.user.role.line',
            'view_mode': 'form',
            'context': {
                'default_th_role_id': self.id
            },
            'target': 'new',
        }

    def copy(self, default=None):
        """Override copy to ensure unique code"""
        default = dict(default or {})
        if 'code' not in default:
            default['code'] = f"{self.code}_copy"
        if 'name' not in default:
            default['name'] = f"{self.name} (Copy)"
        return super().copy(default)
