<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Tree View -->
        <record id="view_th_role_history_tree" model="ir.ui.view">
            <field name="name">th.role.history.tree</field>
            <field name="model">th.role.history</field>
            <field name="arch" type="xml">
                <tree string="Role History" default_order="th_date desc" create="false" edit="false">
                    <field name="th_date"/>
                    <field name="th_user_id"/>
                    <field name="th_role_id"/>
                    <field name="th_role_line_id" invisible="1"/>

                    <field name="th_action" widget="badge" 
                           decoration-success="th_action in ('activate', 'manual_enable', 'created')"
                           decoration-warning="th_action in ('deactivate', 'manual_disable')"
                           decoration-danger="th_action == 'deleted'"/>
                    <field name="th_performed_by"/>
                    <field name="th_reason"/>
                    <button name="action_view_role_assignment" 
                            string="View Assignment" 
                            type="object" 
                            icon="fa-external-link"
                            attrs="{'invisible': [('th_role_line_id', '=', False)]}"/>
                    <button name="action_view_user" 
                            string="View User" 
                            type="object" 
                            icon="fa-user"/>
                    <button name="action_view_role" 
                            string="View Role" 
                            type="object" 
                            icon="fa-shield"/>
                </tree>
            </field>
        </record>

        <!-- Form View -->
        <record id="view_th_role_history_form" model="ir.ui.view">
            <field name="name">th.role.history.form</field>
            <field name="model">th.role.history</field>
            <field name="arch" type="xml">
                <form string="Role History" create="false" edit="false">
                    <header>
                        <button name="action_view_role_assignment" 
                                string="View Assignment" 
                                type="object" 
                                class="btn-primary"
                                attrs="{'invisible': [('th_role_line_id', '=', False)]}"/>
                        <button name="action_view_user" 
                                string="View User" 
                                type="object" 
                                class="btn-secondary"/>
                        <button name="action_view_role" 
                                string="View Role" 
                                type="object" 
                                class="btn-secondary"/>
                        <field name="th_action" widget="statusbar"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <label for="display_name" class="oe_edit_only"/>
                            <h1>
                                <field name="display_name"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group>
                                <field name="th_user_id"/>
                                <field name="th_role_id"/>
                                <field name="th_date"/>
                            </group>
                            <group>
                                <field name="th_performed_by"/>
                                <field name="th_role_line_id"/>
                                <field name="th_date_formatted"/>
                            </group>
                        </group>
                        
                        <group string="Details">
                            <field name="th_reason" nolabel="1" placeholder="Reason for the change..."/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Calendar View -->
        <record id="view_th_role_history_calendar" model="ir.ui.view">
            <field name="name">th.role.history.calendar</field>
            <field name="model">th.role.history</field>
            <field name="arch" type="xml">
                <calendar string="Role History Calendar" 
                          date_start="th_date" 
                          color="th_action"
                          event_open_popup="true"
                          quick_add="false">
                    <field name="display_name"/>
                    <field name="th_user_id"/>
                    <field name="th_role_id"/>
                    <field name="th_action"/>
                </calendar>
            </field>
        </record>

        <!-- Pivot View -->
        <record id="view_th_role_history_pivot" model="ir.ui.view">
            <field name="name">th.role.history.pivot</field>
            <field name="model">th.role.history</field>
            <field name="arch" type="xml">
                <pivot string="Role History Analysis">
                    <field name="th_user_id" type="row"/>
                    <field name="th_action" type="col"/>
                    <field name="th_date" interval="month" type="col"/>
                </pivot>
            </field>
        </record>

        <!-- Graph View -->
        <record id="view_th_role_history_graph" model="ir.ui.view">
            <field name="name">th.role.history.graph</field>
            <field name="model">th.role.history</field>
            <field name="arch" type="xml">
                <graph string="Role History Statistics" type="line">
                    <field name="th_date" interval="month"/>
                    <field name="th_action" type="col"/>
                </graph>
            </field>
        </record>

        <!-- Search View -->
        <record id="view_th_role_history_search" model="ir.ui.view">
            <field name="name">th.role.history.search</field>
            <field name="model">th.role.history</field>
            <field name="arch" type="xml">
                <search string="Search History">
                    <field name="th_user_id" string="User"/>
                    <field name="th_role_id" string="Role"/>
                    <field name="th_performed_by" string="Performed By"/>
                    <field name="th_reason"/>
                    
                    <!-- <filter string="Today" name="today" 
                            domain="[('th_date', '&gt;=', (context_today()).strftime('%Y-%m-%d 00:00:00')),
                                     ('th_date', '&lt;=', (context_today()).strftime('%Y-%m-%d 23:59:59'))]"/>
                    <filter string="This Week" name="this_week" 
                            domain="[('th_date', '&gt;=', (context_today() + relativedelta(days=-context_today().weekday())).strftime('%Y-%m-%d 00:00:00'))]"/>
                    <filter string="This Month" name="this_month" 
                            domain="[('th_date', '&gt;=', (context_today().replace(day=1)).strftime('%Y-%m-%d 00:00:00'))]"/> -->
                    
                    <separator/>
                    <filter string="Activate" name="activate" domain="[('th_action', '=', 'activate')]"/>
                    <filter string="Deactivate" name="deactivate" domain="[('th_action', '=', 'deactivate')]"/>
                    <filter string="Created" name="created" domain="[('th_action', '=', 'created')]"/>
                    <filter string="Deleted" name="deleted" domain="[('th_action', '=', 'deleted')]"/>
                    <filter string="Manual Changes" name="manual" domain="[('th_action', 'in', ['manual_enable', 'manual_disable'])]"/>
                    
                    <separator/>
                    <group expand="0" string="Group By">
                        <filter string="User" name="group_user" context="{'group_by': 'th_user_id'}"/>
                        <filter string="Role" name="group_role" context="{'group_by': 'th_role_id'}"/>
                        <filter string="Action" name="group_action" context="{'group_by': 'th_action'}"/>
                        <filter string="Performed By" name="group_performed_by" context="{'group_by': 'th_performed_by'}"/>
                        <filter string="Date" name="group_date" context="{'group_by': 'th_date:month'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Action -->
        <record id="action_th_role_history" model="ir.actions.act_window">
            <field name="name">Role History</field>
            <field name="res_model">th.role.history</field>
            <field name="view_mode">tree,form,calendar,pivot,graph</field>
            <field name="context">{'search_default_this_month': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No role history found!
                </p>
                <p>
                    Role history will appear here when roles are activated, 
                    deactivated, or modified.
                </p>
            </field>
        </record>

    </data>
</odoo>
