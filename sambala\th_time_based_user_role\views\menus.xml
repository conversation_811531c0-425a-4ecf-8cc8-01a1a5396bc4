<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Main Menu -->
        <menuitem id="menu_th_time_based_user_role_main" name="Time-Based Roles" parent="base.menu_administration" sequence="50" groups="base.group_system,th_time_based_user_role.group_th_role_manager"/>
        <!-- Role Management Submenu -->
        <menuitem id="menu_th_role_management" name="Role Management" parent="menu_th_time_based_user_role_main" sequence="10"/>
        <!-- Roles Menu -->
        <menuitem id="menu_th_user_role" name="Roles" parent="menu_th_role_management" action="action_th_user_role" sequence="10"/>
        <!-- Role Categories Menu -->
        <menuitem id="menu_th_role_category" name="Role Categories" parent="menu_th_role_management" action="action_th_role_category" sequence="20"/>
        <!-- Assignment Management Submenu -->
        <menuitem id="menu_th_assignment_management" name="Assignment Management" parent="menu_th_time_based_user_role_main" sequence="20"/>
        <!-- Role Assignments Menu -->
        <menuitem id="menu_th_user_role_line" name="Role Assignments" parent="menu_th_assignment_management" action="action_th_user_role_line" sequence="10"/>
        <!-- Role History Menu -->
        <menuitem id="menu_th_role_history" name="Role History" parent="menu_th_assignment_management" action="action_th_role_history" sequence="20"/>
        <!-- Configuration Submenu -->
        <menuitem id="menu_th_role_configuration" name="Configuration" parent="menu_th_time_based_user_role_main" sequence="90" groups="base.group_system"/>
        <!-- User Access to their own roles -->
        <menuitem id="menu_th_my_roles" name="My Time-Based Roles" parent="base.menu_users" action="action_th_user_role_line" sequence="50" groups="th_time_based_user_role.group_th_role_user"/>

    </data>
</odoo>
