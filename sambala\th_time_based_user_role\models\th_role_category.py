# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class ThRoleCategory(models.Model):
    _name = 'th.role.category'
    _description = 'Role Category'
    _order = 'name'
    _parent_name = 'th_parent_id'
    _parent_store = True
    _rec_name = 'complete_name'

    name = fields.Char(
        string='Category Name',
        required=True,
        help="Name of the role category"
    )
    
    description = fields.Text(
        string='Description',
        help="Description of the category"
    )
    
    th_parent_id = fields.Many2one(
        'th.role.category',
        string='Parent Category',
        ondelete='cascade',
        help="Parent category"
    )
    
    th_child_ids = fields.One2many(
        'th.role.category',
        'th_parent_id',
        string='Child Categories',
        help="Child categories"
    )
    
    th_role_ids = fields.One2many(
        'th.user.role',
        'th_category_id',
        string='Roles',
        help="Roles in this category"
    )
    
    complete_name = fields.Char(
        string='Complete Name',
        compute='_compute_complete_name',
        store=True,
        help="Complete name with parent categories"
    )
    
    th_role_count = fields.Integer(
        string='Number of Roles',
        compute='_compute_th_role_count',
        store=True,
        help="Number of roles in this category"
    )
    
    parent_path = fields.Char(index=True)

    @api.depends('name', 'th_parent_id.complete_name')
    def _compute_complete_name(self):
        for category in self:
            if category.th_parent_id:
                category.complete_name = f"{category.th_parent_id.complete_name} / {category.name}"
            else:
                category.complete_name = category.name

    @api.depends('th_role_ids')
    def _compute_th_role_count(self):
        for category in self:
            category.th_role_count = len(category.th_role_ids)

    @api.constrains('th_parent_id')
    def _check_parent_recursion(self):
        if not self._check_recursion():
            raise ValidationError(_('You cannot create recursive categories.'))

    def name_get(self):
        result = []
        for category in self:
            result.append((category.id, category.complete_name))
        return result

    @api.model
    def name_search(self, name='', args=None, operator='ilike', limit=100):
        args = args or []
        domain = []
        if name:
            domain = [
                '|', '|',
                ('name', operator, name),
                ('complete_name', operator, name),
                ('description', operator, name)
            ]
        categories = self.search(domain + args, limit=limit)
        return categories.name_get()

    def action_view_roles(self):
        """Action to view roles in this category"""
        self.ensure_one()
        action = self.env.ref('th_time_based_user_role.action_th_user_role').read()[0]
        action['domain'] = [('th_category_id', '=', self.id)]
        action['context'] = {
            'default_th_category_id': self.id
        }
        return action
