# -*- coding: utf-8 -*-

from datetime import timedelta
from odoo import models, fields, api, _


class ThRoleHistory(models.Model):
    _name = 'th.role.history'
    _description = 'Role Change History'
    _order = 'th_date desc'
    _rec_name = 'display_name'

    th_user_id = fields.Many2one(
        'res.users',
        string='User',
        required=True,
        ondelete='cascade',
        help="User whose role was changed"
    )
    
    th_role_id = fields.Many2one(
        'th.user.role',
        string='Role',
        required=True,
        ondelete='cascade',
        help="Role that was changed"
    )
    
    th_date = fields.Datetime(
        string='Date',
        required=True,
        default=fields.Datetime.now,
        help="Date and time of the change"
    )
    
    th_action = fields.Selection([
        ('activate', 'Activated'),
        ('deactivate', 'Deactivated'),
        ('manual_enable', 'Manually Enabled'),
        ('manual_disable', 'Manually Disabled'),
        ('created', 'Assignment Created'),
        ('deleted', 'Assignment Deleted'),
    ], string='Action', required=True, help="Type of change performed")
    
    th_performed_by = fields.Many2one(
        'res.users',
        string='Performed By',
        required=True,
        default=lambda self: self.env.user,
        help="User who performed the change"
    )
    
    th_reason = fields.Text(
        string='Reason',
        help="Reason for the change"
    )
    
    th_role_line_id = fields.Many2one(
        'th.user.role.line',
        string='Role Assignment',
        ondelete='set null',
        help="Related role assignment"
    )
    
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True
    )
    
    th_date_formatted = fields.Char(
        string='Date Formatted',
        compute='_compute_th_date_formatted',
        store=True,
        help="Formatted date for display"
    )

    @api.depends('th_user_id.name', 'th_role_id.name', 'th_action', 'th_date')
    def _compute_display_name(self):
        for history in self:
            user_name = history.th_user_id.name or ''
            role_name = history.th_role_id.name or ''
            action = dict(history._fields['th_action'].selection).get(history.th_action, '')
            date_str = history.th_date.strftime('%d/%m/%Y %H:%M') if history.th_date else ''
            history.display_name = f"{user_name} - {role_name} - {action} ({date_str})"

    @api.depends('th_date')
    def _compute_th_date_formatted(self):
        for history in self:
            if history.th_date:
                history.th_date_formatted = history.th_date.strftime('%d/%m/%Y %H:%M:%S')
            else:
                history.th_date_formatted = ''

    @api.model
    def create_history_record(self, user_id, role_id, action, reason=None, role_line_id=None):
        """Helper method to create history records"""
        return self.create({
            'th_user_id': user_id,
            'th_role_id': role_id,
            'th_action': action,
            'th_reason': reason,
            'th_role_line_id': role_line_id,
        })

    @api.model
    def get_user_role_history(self, user_id, limit=None):
        """Get role history for a specific user"""
        domain = [('th_user_id', '=', user_id)]
        return self.search(domain, limit=limit)

    @api.model
    def get_role_history(self, role_id, limit=None):
        """Get history for a specific role"""
        domain = [('th_role_id', '=', role_id)]
        return self.search(domain, limit=limit)

    @api.model
    def cleanup_old_history(self, days=365):
        """Clean up old history records"""
        cutoff_date = fields.Datetime.now() - timedelta(days=days)
        old_records = self.search([('th_date', '<', cutoff_date)])
        old_records.unlink()
        return len(old_records)

    def action_view_role_assignment(self):
        """Action to view the related role assignment"""
        self.ensure_one()
        if not self.th_role_line_id:
            return
        
        return {
            'name': _('Role Assignment'),
            'type': 'ir.actions.act_window',
            'res_model': 'th.user.role.line',
            'res_id': self.th_role_line_id.id,
            'view_mode': 'form',
            'target': 'current',
        }

    def action_view_user(self):
        """Action to view the user"""
        self.ensure_one()
        return {
            'name': _('User'),
            'type': 'ir.actions.act_window',
            'res_model': 'res.users',
            'res_id': self.th_user_id.id,
            'view_mode': 'form',
            'target': 'current',
        }

    def action_view_role(self):
        """Action to view the role"""
        self.ensure_one()
        return {
            'name': _('Role'),
            'type': 'ir.actions.act_window',
            'res_model': 'th.user.role',
            'res_id': self.th_role_id.id,
            'view_mode': 'form',
            'target': 'current',
        }
