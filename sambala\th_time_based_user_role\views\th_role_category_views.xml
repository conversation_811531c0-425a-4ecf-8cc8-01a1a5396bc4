<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Tree View -->
        <record id="view_th_role_category_tree" model="ir.ui.view">
            <field name="name">th.role.category.tree</field>
            <field name="model">th.role.category</field>
            <field name="arch" type="xml">
                <tree string="Role Categories">
                    <field name="complete_name"/>
                    <field name="th_role_count" string="Roles"/>
                </tree>
            </field>
        </record>

        <!-- Form View -->
        <record id="view_th_role_category_form" model="ir.ui.view">
            <field name="name">th.role.category.form</field>
            <field name="model">th.role.category</field>
            <field name="arch" type="xml">
                <form string="Role Category">
                    <header>
                        <button name="action_view_roles" 
                                string="View Roles" 
                                type="object" 
                                class="btn-primary"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_roles" 
                                    type="object" 
                                    class="oe_stat_button" 
                                    icon="fa-users">
                                <field name="th_role_count" widget="statinfo" string="Roles"/>
                            </button>
                        </div>
                        
                        <div class="oe_title">
                            <label for="name" class="oe_edit_only"/>
                            <h1>
                                <field name="name" placeholder="Category Name..."/>
                            </h1>
                        </div>
                        
                        <group>
                            <field name="th_parent_id"/>
                            <field name="complete_name" readonly="1"/>
                        </group>
                        
                        <field name="description" placeholder="Description of the category..."/>
                        
                        <notebook>
                            <page string="Child Categories" name="children">
                                <field name="th_child_ids">
                                    <tree>
                                        <field name="name"/>
                                        <field name="th_role_count"/>
                                    </tree>
                                </field>
                            </page>
                            
                            <page string="Roles" name="roles">
                                <field name="th_role_ids">
                                    <tree>
                                        <field name="name"/>
                                        <field name="code"/>
                                        <field name="th_user_count"/>
                                        <field name="th_active_user_count"/>
                                        <field name="active"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Search View -->
        <record id="view_th_role_category_search" model="ir.ui.view">
            <field name="name">th.role.category.search</field>
            <field name="model">th.role.category</field>
            <field name="arch" type="xml">
                <search string="Search Categories">
                    <field name="name" string="Category Name"/>
                    <field name="complete_name" string="Complete Name"/>
                    <field name="description"/>
                    <field name="th_parent_id"/>
                    
                    <filter string="Top Level" name="top_level" domain="[('th_parent_id', '=', False)]"/>
                    <filter string="Has Roles" name="has_roles" domain="[('th_role_count', '>', 0)]"/>
                    <filter string="No Roles" name="no_roles" domain="[('th_role_count', '=', 0)]"/>
                    
                    <separator/>
                    <group expand="0" string="Group By">
                        <filter string="Parent Category" name="group_parent" context="{'group_by': 'th_parent_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Action -->
        <record id="action_th_role_category" model="ir.actions.act_window">
            <field name="name">Role Categories</field>
            <field name="res_model">th.role.category</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first role category!
                </p>
                <p>
                    Categories help organize your roles into logical groups.
                </p>
            </field>
        </record>

    </data>
</odoo>
